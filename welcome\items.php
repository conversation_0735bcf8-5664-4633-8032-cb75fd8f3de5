<?php

	/*
	================================================
	== Items Page
	================================================
	*/

	ob_start(); // Output Buffering Start

	$pageTitle = 'Programs';

	if (isset($_COOKIE['User_Name'])) {

		include 'init.php';

		$do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

		if ($do == 'Manage') {


			$stmt = $con->prepare("SELECT 
										programs.*, 
										categories.Cat_Name  
										
									FROM 
										programs
									INNER JOIN 
										categories 
									ON 
										categories.Cat_ID = programs.Cat_ID 
									ORDER BY 
										Pro_ID DESC");

			// Execute The Statement

			$stmt->execute();

			// Assign To Variable 

			$items = $stmt->fetchAll();

			if (! empty($items)) {

			?>

			<h1 class="text-center">Manage Programs</h1>
			<div class="container">
				<div class="table-responsive">
				<a href="items.php?do=Add" class="btn btn-sm btn-primary but-add">
					<i class="fa fa-plus"></i> New Program
				</a>
					<table class="main-table text-center table table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <td>#ID</td>
                                <td>Program Name</td>
                                <td>short Description</td>
                                <td>Date</td>
                                <td>Downloed</td>
                                <td>Image</td>
                                <td>Count_Download</td>
                                <td>Category</td>
                                <td>Tage</td>
                                <td>Control</td>
                            </tr>
                        </thead>
						<?php
							foreach($items as $item) {
								echo "<tr>";
									echo "<td>" . $item['Pro_ID'] . "</td>";
									echo "<td>" . $item['Name'] . "</td>";
                                    echo "<td>" . $item['Short_Des'] . "</td>";
									echo "<td>" . $item['Date'] ."</td>";
									echo "<td>" . $item['Url_Downloed'] ."</td>";
									echo "<td> <img class = 'image' src='../uploads/images/" . $item['Image'] ."' alt='' /></td>";
                                    echo "<td>" . $item['Count_Download'] ."</td>";
									echo "<td>" . $item['Cat_Name'] ."</td>";
									echo "<td>" . $item['Tags'] ."</td>";
									echo "<td>
										<a href='items.php?do=Edit&itemid=" . $item['Pro_ID'] . "' class='btn btn-success'><i class='fa fa-edit'></i> Edit</a>
										<a href='items.php?do=Delete&itemid=" . $item['Pro_ID'] . "' class='btn btn-danger confirm'><i class='fa fa-close'></i> Delete </a>";
									echo "</td>";
								echo "</tr>";
							}
						?>
						<tr>
					</table>
				</div>
			</div>

			<?php } else {

				echo '<div class="container">';
					echo '<div class="nice-message">There\'s No Programs To Show</div>';
					echo '<a href="items.php?do=Add" class="btn btn-sm btn-primary">
							<i class="fa fa-plus"></i> New Program
						</a>';
				echo '</div>';

			} ?>

		<?php 

		} elseif ($do == 'Add') { ?>

			<h1 class="text-center">Add New Program</h1>
			<div class="container">
				<form class="form-horizontal" action="?do=Insert" method="POST" enctype="multipart/form-data">
					<!-- Start Name Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Name</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="name" 
								class="form-control" 
								required="required"  
								placeholder="Name of The Program" />
						</div>
					</div>
					<!-- End Name Field -->
                    <!-- Start Short_Description Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Short_Description</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="shortdescription" 
								class="form-control" 
								required="required"  
								placeholder="Short Description of The Program" />
						</div>
					</div>
					<!-- End Short_Description Field -->
					<!-- Start Description Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Description</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="description" 
								class="form-control" 
								required="required"  
								placeholder="Description of The Program" />
						</div>
					</div>
					<!-- End Description Field -->
					<!-- Start Downloed Field -->
                    <div class="form-group form-group-lg">
                        <label class="col-sm-2 control-label">File Upload</label>
                        <div class="col-sm-10 col-md-6">
                            <input type="file" name="upload" class="form-control" required="required" value="<?php echo $item['Url_Downloed']?>"/>
                        </div>
                    </div>
				    <!-- End Downloed Field -->
					<!-- Start Image Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Image</label>
						<div class="col-sm-10 col-md-6">
							<input type="file" name="image" class="form-control" required="required" />
						</div>
					</div>
					<!-- End Image Field -->
					<!-- Start Categories Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Category</label>
						<div class="col-sm-10 col-md-6">
							<select name="category">
								<option value="0">...</option>
								<?php
									$allCats = getAllFrom("*", "categories", "", "", "Cat_ID");
									foreach ($allCats as $cat) {
										echo "<option value='" . $cat['Cat_ID'] . "'>" . $cat['Cat_Name'] . "</option>";
									}
								?>
							</select>
						</div>
					</div>
					<!-- End Categories Field -->
					<!-- Start Tags Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Tags</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="tags" 
								class="form-control" 
								placeholder="Separate Tags With Comma (,)" />
						</div>
					</div>
					<!-- End Tags Field -->
					<!-- Start Submit Field -->
					<div class="form-group form-group-lg">
						<div class="col-sm-offset-2 col-sm-10">
							<input type="submit" value="Add Program" class="btn btn-primary btn-sm" />
						</div>
					</div>
					<!-- End Submit Field -->
				</form>
			</div>

			<?php

		} elseif ($do == 'Insert') {

			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				echo "<h1 class='text-center'>Insert Program</h1>";
				echo "<div class='container'>";
                
				// Upload image
				$imageName = $_FILES['image']['name'];
				$imageSize = $_FILES['image']['size'];
				$imageTmp	= $_FILES['image']['tmp_name'];
				$imageType = $_FILES['image']['type'];
                
                //upload file
                $fileName = $_FILES['upload']['name'];
				$fileSize = $_FILES['upload']['size'];
				$fileTmp	= $_FILES['upload']['tmp_name'];
				$fileType = $_FILES['upload']['type'];

				// List Of Allowed File Typed To Upload

				$imageAllowedExtension = array("jpeg", "jpg", "png", "gif");
                $fileAllowedExtension = array("rar", "zip");
                
                
				// Get image Extension

				$exploded = explode('.', $imageName);
				$last_element = end($exploded);
				$imageExtension=strtolower($last_element);
                
                // Get file Extension

				$explodedfile = explode('.', $fileName);
				$last_elementfile = end($explodedfile);
				$fileExtension=strtolower($last_elementfile);

				// Get Variables From The Form

				$name		= $_POST['name'];
                $short_desc = $_POST['shortdescription'];
				$desc 		= $_POST['description'];
				$cat 		= $_POST['category'];
				$tags 		= $_POST['tags'];

				// Validate The Form

				$formErrors = array();

				if (empty($name)) {
					$formErrors[] = 'Name Can\'t be <strong>Empty</strong>';
				}
                
                if (empty($short_desc)) {
					$formErrors[] = 'Short Description Can\'t be <strong>Empty</strong>';
				}

				if (empty($desc)) {
					$formErrors[] = 'Description Can\'t be <strong>Empty</strong>';
				}

				if ($cat == 0) {
					$formErrors[] = 'You Must Choose the <strong>Category</strong>';
				}

				if (! empty($imageName) && ! in_array($imageExtension, $imageAllowedExtension)) {
					$formErrors[] = 'This Extension Image Is Not <strong>Allowed</strong>';
				}

				if (empty($imageName)) {
					$formErrors[] = 'Image Is <strong>Required</strong>';
				}

				if ($imageSize > 4194304) {
					$formErrors[] = 'Image Cant Be Larger Than <strong>4MB</strong>';
				}
                
                if ($fileSize > 41943040) {
					$formErrors[] = 'Image Cant Be Larger Than <strong>40MB</strong>';
				}
                
                   if (! empty($fileName) && ! in_array($fileExtension, $fileAllowedExtension)) {
					$formErrors[] = 'This Extension File Is Not <strong>Allowed</strong>';
				}

				if (empty($fileName)) {
					$formErrors[] = 'File Is <strong>Required</strong>';
				}

				// Loop Into Errors Array And Echo It

				foreach($formErrors as $error) {
					echo '<div class="alert alert-danger">' . $error . '</div>';
				}

				// Check If There's No Error Proceed The Update Operation

				if (empty($formErrors)) {

					$image = rand(0, 10000000000) . '_' . $imageName;

					move_uploaded_file($imageTmp, "..\uploads\images\\" . $image);

                    $file = rand(0, 10000000000) . '_' . $fileName;

					move_uploaded_file($fileTmp, "..\uploads\\files\\" . $file);
					// Insert Userinfo In Database

					$stmt = $con->prepare("INSERT INTO 

						programs(Name, Short_Des, Description, Date, Url_Downloed, Image ,Cat_ID, Tags)

						VALUES(:zname, :zshort_desc, :zdesc, now(), :zupload, :zimage, :zcat, :ztags)");

					$stmt->execute(array(

						'zname' 	  => $name,
                        'zshort_desc' => $short_desc,
						'zdesc' 	  => $desc,
						'zupload' 	  => $file,
						'zimage'	  => $image,
						'zcat'		  => $cat,
						'ztags'		  => $tags

					));

					// Echo Success Message

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Inserted</div>';

					redirectHome($theMsg, 'back');

				}

			} else {

				echo "<div class='container'>";

				$theMsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($theMsg);

				echo "</div>";

			}

			echo "</div>";

		} elseif ($do == 'Edit') {

			// Check If Get Request item Is Numeric & Get Its Integer Value

			$itemid = isset($_GET['itemid']) && is_numeric($_GET['itemid']) ? intval($_GET['itemid']) : 0;

			// Select All Data Depend On This ID

			$stmt = $con->prepare("SELECT * FROM programs WHERE Pro_ID = ?");

			// Execute Query

			$stmt->execute(array($itemid));

			// Fetch The Data

			$item = $stmt->fetch();

			// The Row Count

			$count = $stmt->rowCount();

			// If There's Such ID Show The Form

			if ($count > 0) { ?>

				<h1 class="text-center">Edit Program</h1>
				<div class="container">
					<form class="form-horizontal" action="?do=Update" method="POST" enctype="multipart/form-data">
						<input type="hidden" name="itemid" value="<?php echo $itemid ?>" />
						<!-- Start Name Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Name</label>
							<div class="col-sm-10 col-md-6">
								<input 
									type="text" 
									name="name" 
									class="form-control" 
									required="required"  
									placeholder="Name of The Item" 
									value="<?php echo $item['Name'] ?>"/>
							</div>
						</div>
						<!-- End Name Field -->
                        <!-- Start Short_Description Field -->
                        <div class="form-group form-group-lg">
                            <label class="col-sm-2 control-label">Short_Description</label>
                            <div class="col-sm-10 col-md-6">
                                <input 
                                    type="text" 
                                    name="shortdescription" 
                                    class="form-control" 
                                    required="required"  
                                    placeholder="Short Description of The Program"
                                    value="<?php echo $item['Short_Des'] ?>"/>
                            </div>
                        </div>
                        <!-- End Short_Description Field -->
						<!-- Start Description Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Description</label>
							<div class="col-sm-10 col-md-6">
								<input 
									type="text" 
									name="description" 
									class="form-control" 
									required="required"  
									placeholder="Description of The Program"
									value="<?php echo $item['Description'] ?>" />
							</div>
						</div>
						<!-- End Description Field -->
						<!-- Start Download Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">File Upload</label>
							<div class="col-sm-10 col-md-6"> 
								<input type="file" name="upload" class="form-control"/>
								<!-- Optionally show the current file name or a link to it -->
								<?php if (!empty($item['Url_Downloed'])): ?>
									<p>Current File: <a href="../uploads/files/<?php echo htmlspecialchars($item['Url_Downloed']); ?>" target="_blank"><?php echo htmlspecialchars($item['Url_Downloed']); ?></a></p>
								<?php endif; ?>
							</div>
						</div>
						<!-- End Download Field -->
						<!-- Start Image Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Program Image</label>
							<div class="col-sm-10 col-md-6">
								<input type="file" name="image" class="form-control"/>
								<!-- Optionally show the current image name or a thumbnail -->
								<?php if (!empty($item['Image'])): ?>
									<p>Current Image: <a href="../uploads/images/<?php echo htmlspecialchars($item['Image']); ?>" target="_blank"><?php echo htmlspecialchars($item['Image']); ?></a></p>
								<?php endif; ?>
							</div>
						</div>
						<!-- End Image Field -->
						<!-- Start Categories Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Category</label>
							<div class="col-sm-10 col-md-6">
								<select name="category">
									<option value="0">...</option>
									<?php
										$allCats = getAllFrom("*", "categories", "", "", "Cat_ID");
										foreach ($allCats as $cat) {
											echo "<option value='" . $cat['Cat_ID'] . "'";
											if ($item['Cat_ID'] == $cat['Cat_ID']) { echo ' selected'; }
											echo ">" . $cat['Cat_Name'] . "</option>";										
										}
									?>
								</select>
							</div>
						</div>
						<!-- End Categories Field -->
						<!-- Start Tags Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Tags</label>
							<div class="col-sm-10 col-md-6">
								<input 
									type="text" 
									name="tags" 
									class="form-control" 
									placeholder="Separate Tags With Comma (,)"
									value="<?php echo $item['Tags'] ?>" />
							</div>
						</div>
						<!-- End Tags Field -->
						<!-- Start Submit Field -->
						<div class="form-group form-group-lg">
							<div class="col-sm-offset-2 col-sm-10">
								<input type="submit" value="Save Program" class="btn btn-primary btn-sm" />
							</div>
						</div>
						<!-- End Submit Field -->
					</form>

				</div>

			<?php

			// If There's No Such ID Show Error Message

			} else {

				echo "<div class='container'>";

				$theMsg = '<div class="alert alert-danger">Theres No Such ID</div>';

				redirectHome($theMsg);

				echo "</div>";

			}			

		} elseif ($do == 'Update') {

			echo "<h1 class='text-center'>Update Program</h1>";
			echo "<div class='container'>";

			if ($_SERVER['REQUEST_METHOD'] == 'POST') {
			
				// Fetch the current record from the database to get the existing file and image names
				$id = $_POST['itemid'];
				$stmt = $con->prepare("SELECT Url_Downloed, Image FROM programs WHERE Pro_ID = ?");
				$stmt->execute([$id]);
				$item = $stmt->fetch();
			
				//upload image
				$imageName = $_FILES['image']['name'];
				$imageSize = $_FILES['image']['size'];
				$imageTmp  = $_FILES['image']['tmp_name'];
			
				//upload file
				$fileName = $_FILES['upload']['name'];
				$fileSize = $_FILES['upload']['size'];
				$fileTmp  = $_FILES['upload']['tmp_name'];
			
				// List Of Allowed File Types To Upload
				$imageAllowedExtension = array("jpeg", "jpg", "png", "gif");
				$fileAllowedExtension = array("rar", "zip");
			
				// Get image Extension
				$imageExtension = strtolower(pathinfo($imageName, PATHINFO_EXTENSION));
			
				// Get file Extension
				$fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
			
				// Get Variables From The Form
				$name       = $_POST['name'];
				$short_desc = $_POST['shortdescription'];
				$desc       = $_POST['description'];
				$cat        = $_POST['category'];
				$tags       = $_POST['tags'];
			
				// Validate The Form
				$formErrors = array();
			
				if (empty($name)) {
					$formErrors[] = 'Name Can\'t be <strong>Empty</strong>';
				}
			
				if (empty($short_desc)) {
					$formErrors[] = 'Short Description Can\'t be <strong>Empty</strong>';
				}
			
				if (empty($desc)) {
					$formErrors[] = 'Description Can\'t be <strong>Empty</strong>';
				}
			
				if ($cat == 0) {
					$formErrors[] = 'You Must Choose the <strong>Category</strong>';
				}
			
				if (!empty($imageName) && !in_array($imageExtension, $imageAllowedExtension)) {
					$formErrors[] = 'This Extension Image Is Not <strong>Allowed</strong>';
				}
			
				if ($imageSize > 4194304) {
					$formErrors[] = 'Image Cant Be Larger Than <strong>4MB</strong>';
				}
			
				if (!empty($fileName) && !in_array($fileExtension, $fileAllowedExtension)) {
					$formErrors[] = 'This Extension File Is Not <strong>Allowed</strong>';
				}
			
				if ($fileSize > 41943040) {
					$formErrors[] = 'File Cant Be Larger Than <strong>40MB</strong>';
				}
			
				// Loop Into Errors Array And Echo It
				foreach ($formErrors as $error) {
					echo '<div class="alert alert-danger">' . $error . '</div>';
				}
			
				// Check If There's No Error Proceed The Update Operation
				if (empty($formErrors)) {
			
					// Process the image if a new one is uploaded
					if (!empty($imageName)) {
						$image = rand(0, 10000000000) . '_' . $imageName;
						move_uploaded_file($imageTmp, "..\uploads\images\\" . $image);
			
						// Delete the old image if a new one is uploaded
						if (!empty($item['Image'])) {
							unlink("..\uploads\images\\" . $item['Image']);
						}
					} else {
						// Keep the old image if no new image is uploaded
						$image = $item['Image'];
					}
			
					// Process the file if a new one is uploaded
					if (!empty($fileName)) {
						$file = rand(0, 10000000000) . '_' . $fileName;
						move_uploaded_file($fileTmp, "..\uploads\\files\\" . $file);
			
						// Delete the old file if a new one is uploaded
						if (!empty($item['Url_Downloed'])) {
							unlink("..\uploads\\files\\" . $item['Url_Downloed']);
						}
					} else {
						// Keep the old file if no new file is uploaded
						$file = $item['Url_Downloed'];
					}
			
					// Update The Database With The New Or Existing File/Image Paths
					$stmt = $con->prepare("UPDATE 
												programs 
											SET 
												Name = ?, 
												Short_Des = ?,
												Description = ?, 
												Url_Downloed = ?,
												Image = ?,
												Cat_ID = ?,
												Tags = ?
											WHERE 
												Pro_ID = ?");
					$stmt->execute(array($name, $short_desc, $desc, $file, $image, $cat, $tags, $id));
			
					// Echo Success Message

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Updated</div>';

					redirectHome($theMsg, 'back');

				}

			} else {

				$theMsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($theMsg);

			}

			echo "</div>";

		} elseif ($do == 'Delete') {

			echo "<h1 class='text-center'>Delete Program</h1>";
			echo "<div class='container'>";

			
				// Check If Get Request Item ID Is Numeric & Get The Integer Value Of It

				$itemid = isset($_GET['itemid']) && is_numeric($_GET['itemid']) ? intval($_GET['itemid']) : 0;

				// Select All Data Depend On This ID
				// Fetch the current record from the database to get the existing file and image names
				
				$stmt = $con->prepare("SELECT Url_Downloed, Image FROM programs WHERE Pro_ID = ?");
				$stmt->execute([$itemid]);
				$item = $stmt->fetch();

				$check = checkItem('Pro_ID', 'programs', $itemid);

				// If There's Such ID Show The Form
				
				if ($check > 0) {

					// Delete the old image if a new one is uploaded
					if (!empty($item['Image'])) {
						unlink("..\uploads\images\\" . $item['Image']);
					}
					// Delete the old file if a new one is uploaded
					if (!empty($item['Url_Downloed'])) {
						unlink("..\uploads\\files\\" . $item['Url_Downloed']);
					}

					$stmt = $con->prepare("DELETE FROM programs WHERE Pro_ID = :zid");

					$stmt->bindParam(":zid", $itemid);

					$stmt->execute();

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Deleted</div>';

					redirectHome($theMsg, 'back');

				} else {

					$theMsg = '<div class="alert alert-danger">This ID is Not Exist</div>';

					redirectHome($theMsg);

				}

			echo '</div>';

		}

		include $tpl . 'footer.php';

	} else {

		header('Location: index.php');

		exit();
	}

	ob_end_flush(); // Release The Output

?>