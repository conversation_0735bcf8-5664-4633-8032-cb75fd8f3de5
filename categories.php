<?php 
	include 'init.php';
?>

<div class="container">
	<div class="row">
		<div class="col-sm-9">
            <?php
                if (isset($_GET['pageid']) && is_numeric($_GET['pageid'])) {
                    $category = $_GET['pageid'];
                    $cats = getAllFrom("*", "categories", "where Cat_ID = {$category}", "", "Cat_ID");
                    if(count($cats) > 0){
                        foreach ($cats as $cat) {
                            echo '<h1 class="text-center">Category ' . $cat['Cat_Name'] . '</h1>';
                        } ?>

                            <?php
                                $allItems = getAllFrom("*", "programs", "where Cat_ID = {$category}", "", "Pro_ID");
                                foreach ($allItems as $item) {
                                    echo '<div class="well well-lg thumbnail item-box">';
                                    echo '<span class="price-tag"> '. $item['Count_Download'] . '</span>';
                                        echo '<div class="row">';
                                        echo '<div class="col-lg-4">';
                                        echo '<img class="img-Rounded Corners image" src="uploads/images/' . $item['Image'] .'" alt="" />';
                                        echo '</div>';
                                        echo '<div class="col-lg-8">';
                                        echo '<h2><a class="title set_mergin" href="items.php?itemid='. $item['Pro_ID'] .'" >'. $item['Name'] .'</a></h2>';
                                        echo '<p class="set_mergin">' . $item['Description'] . '</p>';
                                        echo '<p><a class="btn btn-primary btn-lg set_mergin" href="items.php?itemid='. $item['Pro_ID'] .'" role="button">تحميل</a></p>';
                                        echo '</div>';
                                        echo '</div>';
                                    echo '</div>';
                                }

                    } else { 

                        echo '<div class="alert alert-danger">You Must Add Page ID</div>';

                    }
                }else{
                    echo '<div class="alert alert-danger">You Must Add Page ID</div>';
                }
                    ?>
		</div>
		
<?php include $tpl . 'footer.php'; ?>