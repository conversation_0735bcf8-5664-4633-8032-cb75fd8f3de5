/* Start Main Rulez */

body {
	background-color: #F2F2F2;;
	font-size: 16px;
	height: 3000px;
}

h1 {
	font-size: 40px;
	margin: 20px 0;
	font-weight: bold;
	color: #666;
}

.title {
	color: #252525;
}

.well ,aside{
	margin-top: 1rem;
}

.btn-download{
	margin: 40px;
    padding: inherit;
    font-size: 50px;
}

.input-container {
	position: relative;
}

.asterisk {
    font-size: 20px;
    position: absolute;
    right: 10px;
    top: 7px;
    color: #D20707;
}

.main-form .asterisk {
    font-size: 30px;
    position: absolute;
    right: 30px;
    top: 8px;
    color: #D20707;
}

.nice-message {
	padding: 10px;
	background-color: #FFF;
	margin: 10px 0;
	border-left: 5px solid #080;
}

/* End Main Rulez */

/* Start Bootstrap Edits */

.navbar {
	border-radius: 0;
	margin-bottom: 0;
}

.nav > li > a,
.navbar-brand {
	padding: 15px 12px;
}

.navbar-brand {
	font-size: 2em;
    color: aqua;
}

.navbar-inverse .navbar-nav > .open>a,
.navbar-inverse .navbar-nav > .open>a:focus,
.navbar-inverse .navbar-nav > .open>a:hover,
.dropdown-menu {
    background-color: #3498db;
}

.dropdown-menu {
	min-width: 180px;
	padding: 0;
	font-size: 1em;
	border: none;
	border-radius: 0;
}

.dropdown-menu > li > a {
	color: #FFF;
	padding: 10px 15px;
}

.dropdown-menu > li > a:focus,
.dropdown-menu > li > a:hover {
    color: #FFF;
    background-color: #8e44ad;
}

.form-control {
	position: relative;
}

/* End Bootstrap Edits */

/* Start Header */

.upper-bar {
	padding: 10px;
	background-color: #FFF
}

.image {
	width: 300px;
	height: 300px;
}

/* End Header */

/* Start Login Page */

.login-page form,
.the-errors {
	max-width: 380px;
	margin: auto;
}

.login-page form input {
	margin-bottom: 10px;
}

.login-page [data-class="login"].selected {
	color: #337AB7;
}

.login-page [data-class="signup"].selected {
	color: #5cb85c;
}

.login-page h1 {
	color: #C0C0C0;
}

.login-page h1 span {
	cursor: pointer;
}

.login-page .signup {
	display: none;
}

.the-errors .msg {
    padding: 10px;
    text-align: left;
    background-color: #fff;
    margin-bottom: 8px;
    border-right: 1px solid #e0e0e0;
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    color: #919191;
}

.the-errors .error {
    border-left: 5px solid #cd6858
}

/* End Login Page */

/* Start Categories Page */

.item-box {
	position: relative;
	background-color: #FFF;
}

.item-box .price-tag {
    background-color: #4B4B4B;
    padding: 2px 10px;
    position: absolute;
    right: 0;
    top: 10px;
    font-weight: bold;
    color: #FFF;
}

.item-box .approve-status {
    position: absolute;
    top: 40px;
    left: 0;
    background-color: #b85a5a;
    color: #FFF;
    padding: 3px 5px;
}

.item-box .caption p {
	height: 44px;
	max-height: 44px;
}

/* End Categories Page */

/* Start Show Item Page */

.item-info h2 {
	padding: 10px;
	margin: 0;
}

.item-info p {
	padding: 10px;
}

.item-info ul li { 
	padding: 10px;
}

.item-info ul li:nth-child(odd) {
	background-color: #e8e8e8;
}

.item-info ul li span {
	display: inline-block;
	width: 120px;
}

.tags-items a {
    display: inline-block;
    background-color: #e2e2e2;
    padding: 2px 10px;
    border-radius: 5px;
    color: #666;
    margin-right: 5px;
}


.search{
    padding: 4px 4px;
    margin-top: -5px;
}

.widget-title {
  margin-bottom: 10px;
  font-size: 20px;
  line-height: 1.5;
  text-transform: none;
}

.widget ul li {
  list-style-type: none;
  position: inherit;
  margin-bottom: .5em;
}
/* End Show Item Page */

/* Start Our Custom */

.custom-hr {
	border-top: 1px solid #c9c9c9;
}

/* End Our Custom */
