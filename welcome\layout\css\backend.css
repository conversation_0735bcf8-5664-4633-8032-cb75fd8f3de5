/* Start Main Rulez */

body {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	font-size: 16px;
	min-height: 100vh;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

h1 {
	font-size: 42px;
	margin: 30px 0;
	font-weight: 700;
	color: #2c3e50;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.asterisk {
	font-size: 30px;
    position: absolute;
    right: 30px;
    top: 8px;
    color: #D20707;
}

.nice-message {
	padding: 10px;
	background-color: #FFF;
	margin: 10px 0;
	border-left: 5px solid #080;
}

/* End Main Rulez */

/* Start Login Form */

.login {
	width: 400px;
	margin: 100px auto;
	background: rgba(255, 255, 255, 0.95);
	padding: 40px;
	border-radius: 20px;
	box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.login h4 {
	color: #2c3e50;
	font-weight: 700;
	font-size: 28px;
	margin-bottom: 30px;
	text-align: center;
}

.login input {
	margin-bottom: 20px;
	border-radius: 25px;
	border: 2px solid #e0e0e0;
	padding: 15px 20px;
	transition: all 0.3s ease;
}

.login .form-control {
	background-color: rgba(255, 255, 255, 0.9);
}

.login .form-control:focus {
	border-color: #3498db;
	box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
	outline: none;
}

.login .btn {
	background: linear-gradient(45deg, #3498db, #2980b9);
	border: none;
	border-radius: 25px;
	padding: 15px 30px;
	font-weight: 600;
	font-size: 16px;
	width: 100%;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.login .btn:hover {
	background: linear-gradient(45deg, #2980b9, #3498db);
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(52, 152, 219, 0.5);
}

/* End Login Form */

/* Start Bootstrap Edits */

.navbar {
	border-radius: 0;
	margin-bottom: 0;
	background: rgba(44, 62, 80, 0.95) !important;
	backdrop-filter: blur(10px);
	box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
	border: none;
}

.nav > li > a,
.navbar-brand {
	padding: 15px 20px;
	transition: all 0.3s ease;
}

.navbar-brand {
	font-size: 1.5em;
	color: #ecf0f1 !important;
	font-weight: 700;
}

.nav-link {
	color: #ecf0f1 !important;
	font-weight: 500;
	position: relative;
}

.nav-link:hover {
	color: #3498db !important;
}

.nav-link::after {
	content: '';
	position: absolute;
	width: 0;
	height: 2px;
	bottom: 0;
	left: 50%;
	background-color: #3498db;
	transition: all 0.3s ease;
}

.nav-link:hover::after {
	width: 100%;
	left: 0;
}

.navbar-inverse .navbar-nav > .open>a,
.navbar-inverse .navbar-nav > .open>a:focus,
.navbar-inverse .navbar-nav > .open>a:hover,
.dropdown-menu {
    background-color: #3498db;
}

.dropdown-menu {
	min-width: 180px;
	padding: 0;
	font-size: 1em;
	border: none;
	border-radius: 0;
}

.dropdown-menu > li > a {
	color: #FFF;
	padding: 10px 15px;
}

.dropdown-menu > li > a:focus,
.dropdown-menu > li > a:hover {
    color: #FFF;
    background-color: #8e44ad;
}

.form-control {
	position: relative;
}

.btn-logout {
    margin: 8px;
}

/* End Bootstrap Edits */

/* Start Dashboard Page */

.home-stats .stat {
	padding: 30px;
	font-size: 16px;
	color: #FFF;
	border-radius: 20px;
	position: relative;
	overflow: hidden;
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.home-stats .stat:hover {
	transform: translateY(-5px);
	box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.home-stats .stat i {
    position: absolute;
    font-size: 80px;
    top: 35px;
    left: 30px;
}

.home-stats .stat .info {
	float: right;
}

.home-stats .stat a {
	color: #FFF;
}

.home-stats .stat a:hover {
	text-decoration: none;
}

.home-stats .stat span {
	display: block;
	font-size: 60px;
}

.home-stats .st-request {
	background: linear-gradient(135deg, #3498db, #2980b9);
}

.home-stats .st-pending {
	background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.home-stats .st-items {
	background: linear-gradient(135deg, #f39c12, #d35400);
}

.home-stats .st-comments {
	background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.latest {
	margin-top: 30px;
}

.latest .toggle-info {
	color: #999;
	cursor: pointer
}

.latest .toggle-info:hover {
	color: #444;
}

.latest-users {
	margin-bottom: 0;
}

.latest-users li {
	padding: 10px;
	overflow: hidden;
}



/* End Dashboard Page */

/* Start Category Page */

.categories .panel-heading {
	color: #959595;
	font-weight: bold;
}

.categories .panel-heading i {
	position: relative;
	top: 1px;
}

.categories .panel-body {
	padding: 0;
}

.categories .option a {
	color: #888;
	text-decoration: none;
}

.categories .option span {
	color: #888;
	cursor: pointer;
}

.categories .option .active {
	color: #F00
}

.categories hr {
	margin-top: 0;
	margin-bottom: 0;
}

.categories .cat {
	padding: 15px;
	position: relative;
	overflow: hidden;
}

.categories .cat:hover {
	background-color: #EEE;
}

.categories .cat:hover .hidden-buttons {
	right: 10px;
}

.categories .cat .hidden-buttons {
	-webkit-transition: all .5s ease-in-out;
	-moz-transition: all .5s ease-in-out;
	transition: all .5s ease-in-out;
	position: absolute;
	top: 15px;
	right: -120px;
}

.categories .cat .hidden-buttons a {
	margin-right: 5px;
}

.categories .cat h3 {
	margin: 0;
	cursor: pointer;
    font-weight: bold;
    color: #6A6A6A;
}

.categories .cat .full-view p {
	margin: 10px 0;
	color: #707070;
}

.categories .cat:last-of-type ~ hr {
	display: none;
}

.categories .cat .cat-span {
    color: #FFF;
    padding: 4px 6px;
    margin-right: 6px;
    border-radius: 6px
}

.categories .cat .visibility {
    background-color: #d35400
}

.categories .cat .commenting {
    background-color: #2c3e50
}

.categories .cat .advertises {
    background-color: #c0392b
}

.categories .add-category {
	margin-top: -10px;
	margin-bottom: 30px;
}

.categories .child-head {
    margin: 15px 0 10px;
    font-weight: bold;
    font-size: 16px;
    color: #22ab79;
}

.categories .child-cats {
	margin: 0;
}

.categories .child-cats li {
	margin-left: 15px;
}

.categories .child-cats li:before {
	content: "- ";
}

.categories .show-delete {
	color: #F00;
	display: none;
}

.thead-dark {
    background-color: #5c5c5c;
    color: #FFF;
}
/* End Category Page */

/* Start Program Page*/

.image {
    width: 150px;
	height: 150px;
}
/* End Program Page*/

.but-add{
    float: right;
}

/* Additional Modern Styles for Admin Panel */
.panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: none;
    backdrop-filter: blur(10px);
}

.panel-heading {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 20px;
    font-weight: 600;
}

.btn-logout {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-logout:hover {
    background: linear-gradient(45deg, #c0392b, #e74c3c);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
    outline: none;
}

.table {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.table th {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    font-weight: 600;
    border: none;
    padding: 15px;
}

.table td {
    padding: 15px;
    border: none;
    border-bottom: 1px solid #ecf0f1;
}