/* Start Main Rulez */

body {
	background-color: #F4F4F4;
	font-size: 16px;
}

h1 {
	font-size: 40px;
	margin: 20px 0;
	font-weight: bold;
	color: #666;
}

.asterisk {
	font-size: 30px;
    position: absolute;
    right: 30px;
    top: 8px;
    color: #D20707;
}

.nice-message {
	padding: 10px;
	background-color: #FFF;
	margin: 10px 0;
	border-left: 5px solid #080;
}

/* End Main Rulez */

/* Start Login Form */

.login {
	width: 300px;
	margin: 100px auto;
}

.login h4 {
	color: #888;
}

.login input {
	margin-bottom: 10px;
}

.login .form-control {
	background-color: #EAEAEA;
}

.login .btn {
	background-color: #008dde;
	display: block;
	margin: 0 auto;
}

/* End Login Form */

/* Start Bootstrap Edits */

.navbar {
	border-radius: 0;
	margin-bottom: 0;
}

.nav > li > a,
.navbar-brand {
	padding: 15px 12px;
}

.navbar-brand {
	font-size: 1em;
}

.navbar-inverse .navbar-nav > .open>a,
.navbar-inverse .navbar-nav > .open>a:focus,
.navbar-inverse .navbar-nav > .open>a:hover,
.dropdown-menu {
    background-color: #3498db;
}

.dropdown-menu {
	min-width: 180px;
	padding: 0;
	font-size: 1em;
	border: none;
	border-radius: 0;
}

.dropdown-menu > li > a {
	color: #FFF;
	padding: 10px 15px;
}

.dropdown-menu > li > a:focus,
.dropdown-menu > li > a:hover {
    color: #FFF;
    background-color: #8e44ad;
}

.form-control {
	position: relative;
}

.btn-logout {
    margin: 8px;
}

/* End Bootstrap Edits */

/* Start Dashboard Page */

.home-stats .stat {
	padding: 20px;
	font-size: 15px;
	color: #FFF;
	border-radius: 10px;
	position: relative;
	overflow: hidden;
}

.home-stats .stat i {
    position: absolute;
    font-size: 80px;
    top: 35px;
    left: 30px;
}

.home-stats .stat .info {
	float: right;
}

.home-stats .stat a {
	color: #FFF;
}

.home-stats .stat a:hover {
	text-decoration: none;
}

.home-stats .stat span {
	display: block;
	font-size: 60px;
}

.home-stats .st-request {
	background-color: #3498db;
}

.home-stats .st-pending {
	background-color: #c0392b;
}

.home-stats .st-items {
	background-color: #d35400;
}

.home-stats .st-comments {
	background-color: #8e44ad;
}

.latest {
	margin-top: 30px;
}

.latest .toggle-info {
	color: #999;
	cursor: pointer
}

.latest .toggle-info:hover {
	color: #444;
}

.latest-users {
	margin-bottom: 0;
}

.latest-users li {
	padding: 10px;
	overflow: hidden;
}



/* End Dashboard Page */

/* Start Category Page */

.categories .panel-heading {
	color: #959595;
	font-weight: bold;
}

.categories .panel-heading i {
	position: relative;
	top: 1px;
}

.categories .panel-body {
	padding: 0;
}

.categories .option a {
	color: #888;
	text-decoration: none;
}

.categories .option span {
	color: #888;
	cursor: pointer;
}

.categories .option .active {
	color: #F00
}

.categories hr {
	margin-top: 0;
	margin-bottom: 0;
}

.categories .cat {
	padding: 15px;
	position: relative;
	overflow: hidden;
}

.categories .cat:hover {
	background-color: #EEE;
}

.categories .cat:hover .hidden-buttons {
	right: 10px;
}

.categories .cat .hidden-buttons {
	-webkit-transition: all .5s ease-in-out;
	-moz-transition: all .5s ease-in-out;
	transition: all .5s ease-in-out;
	position: absolute;
	top: 15px;
	right: -120px;
}

.categories .cat .hidden-buttons a {
	margin-right: 5px;
}

.categories .cat h3 {
	margin: 0;
	cursor: pointer;
    font-weight: bold;
    color: #6A6A6A;
}

.categories .cat .full-view p {
	margin: 10px 0;
	color: #707070;
}

.categories .cat:last-of-type ~ hr {
	display: none;
}

.categories .cat .cat-span {
    color: #FFF;
    padding: 4px 6px;
    margin-right: 6px;
    border-radius: 6px
}

.categories .cat .visibility {
    background-color: #d35400
}

.categories .cat .commenting {
    background-color: #2c3e50
}

.categories .cat .advertises {
    background-color: #c0392b
}

.categories .add-category {
	margin-top: -10px;
	margin-bottom: 30px;
}

.categories .child-head {
    margin: 15px 0 10px;
    font-weight: bold;
    font-size: 16px;
    color: #22ab79;
}

.categories .child-cats {
	margin: 0;
}

.categories .child-cats li {
	margin-left: 15px;
}

.categories .child-cats li:before {
	content: "- ";
}

.categories .show-delete {
	color: #F00;
	display: none;
}

.thead-dark {
    background-color: #5c5c5c;
    color: #FFF;
}
/* End Category Page */

/* Start Program Page*/

.image {
    width: 150px;
	height: 150px;
}
/* End Program Page*/

.but-add{
    float: right;
}