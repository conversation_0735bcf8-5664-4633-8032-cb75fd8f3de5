 <div class="col-sm-3" id="myScrollspy">
        <main>
          <aside id="search-2" class="widget inner-padding widget_search thumbnail color-aside"><form method="get" class="search-form" action="search.php">
            <label>
                <input type="search" class="search-field" placeholder="Search &hellip;" value="" name="s" title="Search for:">
                <input class="btn btn-success btn-sm glyphicon glyphicon-search search" type="submit" value="بحث"/>
              </label></form>
          </aside>
          <aside id="nav_menu-2" class="widget inner-padding widget_nav_menu thumbnail color-aside">
            <h2 class="widget-title">Top Software</h2>
            <div class="menu-top-software-container">
              <ul id="menu-top-software" class="menu">
              <?php
                $topItems = getTopform("*", "programs", "Count_Download");
                foreach ($topItems as $item) {
                     echo '<li class="menu-item">';
                      echo '<a href="items.php?itemid='. $item['Pro_ID'] .'">' . $item['Name'] . '</a>';
                    echo '</li>'; 
                   }?>
              </ul>
            </div>
          </aside>
        </main>
        </div>
    </div>
</div>

<div class="footer"></div>
        <script src="<?php echo $js ?>jquery-3.6.1.min.js"></script>
		<script src="<?php echo $js ?>jquery-ui.min.js"></script>
		<script src="<?php echo $js ?>bootstrap.min.js"></script>
		<script src="<?php echo $js ?>jquery.selectBoxIt.min.js"></script>
		<script src="<?php echo $js ?>front.js"></script>
	</body>
</html>