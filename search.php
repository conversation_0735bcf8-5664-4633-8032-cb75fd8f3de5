<?php
    $pageTitle = 'Search';
	include 'init.php';

	
	echo '<div class="container">';
        echo '<div class="row">';
            echo '<div class="col-sm-9">';
                echo '<h1 class="text-center">نتائج البحث</h1>';
	
	if(isset($_GET['s']) && is_string($_GET['s']) && strlen($_GET['s']) >= 3){ 
        
        $query = filter_var($_GET['s'],FILTER_SANITIZE_SPECIAL_CHARS);
        $stmt = $con->prepare("SELECT 
                                    programs.*, 
                                    categories.Cat_Name

                                FROM 
                                    programs
                                INNER JOIN 
                                    categories 
                                ON 
                                    categories.Cat_ID = programs.Cat_ID 
                                WHERE 
                                    (`Name` LIKE '%".$query."%') OR (`programs`.`Description` LIKE '%".$query."%') OR (`Tags` LIKE '%".$query."%') OR (`Cat_Name` LIKE '%".$query."%')
                                ORDER BY 
                                    Pro_ID DESC");

			// Execute The Statement

			$stmt->execute();

			// Assign To Variable 

			$items = $stmt->fetchAll();
        
        	$count = $stmt->rowCount();
        
		if($count > 0){ // if one or more rows are returned do following
        
             foreach ($items as $item) {
              echo '<div class="well well-lg thumbnail item-box">';
                echo '<span class="price-tag"> '. $item['Count_Download'] . '</span>';
                echo '<div class="row">';
                  echo '<div class="col-lg-4">';
                    echo '<img class="img-Rounded Corners image" src="uploads/images/' . $item['Image'] .'" alt="" />';
                  echo '</div>';
                  echo '<div class="col-lg-8">';
                    echo '<h2><a class="title set_mergin" href="items.php?itemid='. $item['Pro_ID'] .'" >'. $item['Name'] .'</a></h2>';
                    echo '<p class ="set_mergin">' . $item['Description'] . '</p>';
                    echo '<p><a class="btn btn-primary btn-lg set_mergin" href="items.php?itemid='. $item['Pro_ID'] .'" role="button">تحميل</a></p>';
                  echo '</div>';
                echo '</div>';
              echo '</div>';
                   
            }
			
			
		}
		else{// if there is no matching rows do following
    
            echo '<div class="alert alert-danger">No results</div>';
		}
		
	}
	else{ // if query length is less than minimum
        
        echo '<div class="alert alert-danger">Minimum length is 3</div>';
	}
    echo '</div>';
    include $tpl . 'footer.php';
	ob_end_flush();
?>