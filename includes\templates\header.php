<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8" />
		<title><?php getTitle() ?></title>
		<link rel="stylesheet" href="<?php echo $css ?>bootstrap.min.css" />
		<link rel="stylesheet" href="<?php echo $css ?>font-awesome.min.css" />
		<link rel="stylesheet" href="<?php echo $css ?>jquery-ui.css" />
		<link rel="stylesheet" href="<?php echo $css ?>jquery.selectBoxIt.css" />
		<link rel="stylesheet" href="<?php echo $css ?>front.css" />
	</head>
	<body>
        <nav class="navbar navbar-expand-sm navbar-dark bg-dark">
          <div class="container-fluid">
           <ul class="navbar-nav">
              <li class="nav-item">
                <a class="nav-link active" href="index.php">Falak</a>
              </li>
            </ul>
            <ul class="navbar-nav justify-content-end ">
                <?php
                    $allCats = getAllFrom("*", "categories", "", "", "Cat_ID", "ASC");
                    foreach ($allCats as $cat) {
                        echo 
                        '<li >
                            <a class="nav-link px-2 text-white" href="categories.php?pageid=' . $cat['Cat_ID'] . '">
                                ' . $cat['Cat_Name'] . '
                            </a>
                        </li>';
                    }
                  ?>
            </ul>
          </div>
        </nav>

        
  