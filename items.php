<?php
	$pageTitle = 'Show Crack';
	include 'init.php';

	// Check If Get Request item Is Numeric & Get Its Integer Value
	$itemid = isset($_GET['itemid']) && is_numeric($_GET['itemid']) ? intval($_GET['itemid']) : 0;

	// Select All Data Depend On This ID
	$stmt = $con->prepare("SELECT 
								programs.*, 
								categories.Cat_Name 
							FROM 
								programs
							INNER JOIN 
								categories 
							ON 
								categories.Cat_ID = programs.Cat_ID  
							WHERE 
								Pro_ID = ?");

	// Execute Query
	$stmt->execute(array($itemid));

	$count = $stmt->rowCount();?>
    <div class="container">
	   <div class="row">
        <div class="col-sm-9">
    <?php
	if ($count > 0) {

	// Fetch The Data
	$item = $stmt->fetch();
?>
            <h1 class="text-center"><?php echo $item['Name'] ?></h1>
		<div class="container text-center">
			<img class="img-responsive img-thumbnail center-block" src='uploads/images/<?php echo $item["Image"] ?>' alt="" />
		</div>
		<div class="container text-center">
			<a class="btn btn-success btn-lg btn-download " href="download.php?downid=<?php echo $item['Pro_ID'] ?>" role="button">
            <i class="fa fa-download"></i> تحميل</a>
		</div>
			<p><?php echo $item['Description'] ?></p>
			<span>Added Date</span> : <?php echo $item['Date'] ?>
			<div>
			<span>Tags</span> : 
			<?php 
				$allTags = explode(",", $item['Tags']);
				foreach ($allTags as $tag) {
					$tag = str_replace(' ', '', $tag);
					$lowertag = strtolower($tag);
					if (! empty($tag)) {
						echo "<a href='tags.php?name={$lowertag}'>" . $tag . '</a>';
					}
				}
			?>
			</div>
        
<?php
	} else {
		
        echo '<div class="alert alert-danger">There\'s no Such ID Or This Item Is Waiting Approval</div>';
		
	}
    echo '</div>';
	include $tpl . 'footer.php';
	ob_end_flush();
?>