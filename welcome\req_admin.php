<?php

    ob_start(); // Output Buffering Start

	$pageTitle = 'Items';

	if (isset($_COOKIE['User_Name'])) {

		include 'init.php';

		$do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

		if ($do == 'Manage') {


			$stmt = $con->prepare("SELECT 
										*
									FROM 
										req_admin 
									ORDER BY 
										Req_ID DESC");

			// Execute The Statement

			$stmt->execute();

			// Assign To Variable 

			$reqs = $stmt->fetchAll();

			if (! empty($reqs)) {

			?>

			<h1 class="text-center">Manage request admin</h1>
			<div class="container">
				<div class="table-responsive">
					<table class="main-table text-center table table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <td>#ID</td>
                                <td>IP</td>
                                <td>Country</td>
                                <td>Date</td>
                                <td>Count</td>
                                <td>Control</td>
                            </tr>
                        </thead>
						<?php
							foreach($reqs as $req) {
								echo "<tr>";
									echo "<td>" . $req['Req_ID'] . "</td>";
									echo "<td>" . $req['Req_ip'] . "</td>";
									echo "<td>" . $req['Country'] . "</td>";
									echo "<td>" . $req['Date'] ."</td>";
                                    echo "<td>" . $req['count'] ."</td>";
									echo "<td>
										<a href='req_admin.php?do=Update&reqid=" . $req['Req_ID'] . "' class='btn btn-success'><i class='fa fa-edit'></i> Reset</a>
										<a href='req_admin.php?do=Block&reqid=" . $req['Req_ID'] . "' class='btn btn-danger confirm'><i class='fa fa-close'></i> Block </a>";
									echo "</td>";
								echo "</tr>";
							}
						?>
						<tr>
					</table>
				</div>
			</div>

			<?php } else {

				echo '<div class="container">';
					echo '<div class="nice-message">There\'s No Request To Show</div>';
				echo '</div>';

			} ?>

		<?php 

		}elseif ($do == 'Update') {
            
            echo "<h1 class='text-center'>Update request</h1>";
			echo "<div class='container'>";
            // Check If Get Request item Is Numeric & Get Its Integer Value

			$reqid = isset($_GET['reqid']) && is_numeric($_GET['reqid']) ? intval($_GET['reqid']) : 0;

			// Select All Data Depend On This ID

			$stmt = $con->prepare("SELECT * FROM req_admin WHERE Req_ID = ?");

			// Execute Query

			$stmt->execute(array($reqid));

			// Fetch The Data

			$req = $stmt->fetch();

			// The Row Count

			$count = $stmt->rowCount();

			// If There's Such ID Show The Form

			if ($count > 0) { 
                
                $id 		= $req['Req_ID'];
                $count = 0 ;
            
                $stmt = $con->prepare("UPDATE 
                                            req_admin 
                                        SET 
                                            count = ?
                                        WHERE 
                                            Req_ID = ?");

                $stmt->execute(array($count, $id));

                // Echo Success Message

                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Request Updated</div>';

                redirectHome($theMsg, 'back');
            } else {

            echo "<div class='container'>";

            $theMsg = '<div class="alert alert-danger">Theres No Such ID</div>';

            redirectHome($theMsg);

            echo "</div>";

        }			

			

		} elseif ($do == 'Block') {

			echo "<h1 class='text-center'>Block request</h1>";
			echo "<div class='container'>";
            // Check If Get Request item Is Numeric & Get Its Integer Value

			$reqid = isset($_GET['reqid']) && is_numeric($_GET['reqid']) ? intval($_GET['reqid']) : 0;

			// Select All Data Depend On This ID

			$stmt = $con->prepare("SELECT * FROM req_admin WHERE Req_ID = ?");

			// Execute Query

			$stmt->execute(array($reqid));

			// Fetch The Data

			$req = $stmt->fetch();

			// The Row Count

			$count = $stmt->rowCount();

			// If There's Such ID Show The Form

			if ($count > 0) { 
                
                $id 		= $reqid;
                $count = 5 ;
            
                $stmt = $con->prepare("UPDATE 
                                            req_admin 
                                        SET 
                                            count = ?
                                        WHERE 
                                            Req_ID = ?");

                $stmt->execute(array($count, $id));

                // Echo Success Message

                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Block request</div>';

                redirectHome($theMsg, 'back');
            } else {

            echo "<div class='container'>";

            $theMsg = '<div class="alert alert-danger">Theres No Such ID</div>';

            redirectHome($theMsg);

        }			

			echo '</div>';

		}
    }else {

		header('Location: index.php');

		exit();
	}

	ob_end_flush(); // Release The Output

?>