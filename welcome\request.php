<?php

    ob_start(); // Output Buffering Start

	$pageTitle = 'Items';

	if (isset($_COOKIE['User_Name'])) {

		include 'init.php';

		$do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

		if ($do == 'Manage') {


			$stmt = $con->prepare("SELECT 
										*
									FROM 
										request 
									ORDER BY 
										Req_ID DESC");

			// Execute The Statement

			$stmt->execute();

			// Assign To Variable 

			$reqs = $stmt->fetchAll();

			if (! empty($reqs)) {

			?>

			<h1 class="text-center">Manage request</h1>
			<div class="container">
				<div class="table-responsive">
					<table class="main-table text-center table table-bordered">
                        <thead class="thead-dark">
                            <tr>
                                <td>#ID</td>
                                <td>IP</td>
                                <td>Country</td>
                                <td>Date</td>
                            </tr>
                        </thead>
						<?php
							foreach($reqs as $req) {
								echo "<tr>";
									echo "<td>" . $req['Req_ID'] . "</td>";
									echo "<td>" . $req['Req_ip'] . "</td>";
									echo "<td>" . $req['Country'] . "</td>";
									echo "<td>" . $req['Date'] ."</td>";
									echo "</td>";
								echo "</tr>";
							}
						?>
						<tr>
					</table>
				</div>
			</div>

			<?php } else {

				echo '<div class="container">';
					echo '<div class="nice-message">There\'s No Request To Show</div>';
				echo '</div>';

			} ?>

		<?php 

		}
    }else {

		header('Location: index.php');

		exit();
	}

	ob_end_flush(); // Release The Output

?>