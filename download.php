<?php
$pageTitle = 'Show Crack';
	include 'init.php';

	// Check If Get Request item Is Numeric & Get Its Integer Value
	$itemid = isset($_GET['downid']) && is_numeric($_GET['downid']) ? intval($_GET['downid']) : 0;

// Select All Data Depend On This ID
	$stmt = $con->prepare("SELECT 
								programs.*, 
								categories.Cat_Name 
							FROM 
								programs
							INNER JOIN 
								categories 
							ON 
								categories.Cat_ID = programs.Cat_ID  
							WHERE 
								Pro_ID = ?");

	// Execute Query
	$stmt->execute(array($itemid));

	$count = $stmt->rowCount();

	if ($count > 0) {

	// Fetch The Data
	$item = $stmt->fetch();
    

    $hit_count = $item['Count_Download']; // read the hit count from file

    $hit_count++; // increment the hit count by 1
        
   
    $stmt = $con->prepare("UPDATE 
                                programs 
                            SET 
                                Count_Download =?
                            WHERE 
                                Pro_ID = ?");

    $stmt->execute(array($hit_count, $itemid));

    header('location: uploads\files\\' .$item['Url_Downloed']);
    
    } else {
    echo '<div class="container">';
        echo '<div class="alert alert-danger">There\'s no Such ID Or This Item Is Waiting Approval</div>';
    echo '</div>';
	}
    
?>