Options -Indexes
ErrorDocument 404 /freecrack/404.php
ErrorDocument 403 /freecrack/403.php

<Files .htaccess>
    Order Allow,<PERSON>y
    Deny from all
</Files>

<FilesMatch "^\.env">
    Order Allow,Deny
    Deny from all
</FilesMatch>


<FilesMatch "\.(ini|log|sh|sql|bak|json|yaml)$">
    Order Allow,<PERSON>y
    Deny from all
</FilesMatch>


ServerSignature Off


RewriteEngine On
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
RewriteRule \.(jpeg|png|gif)$ - [F]


php_value upload_max_filesize 10G
php_value post_max_size 10G


RewriteEngine On
RewriteCond %{HTTPS} !=on
RewriteRule ^ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]


Header always append X-Frame-Options DENY

Header set X-XSS-Protection "1; mode=block"

Header set Content-Security-Policy "default-src 'self'; script-src 'self' https://trusted.com; style-src 'self' 'unsafe-inline';"

Header set X-Content-Type-Options nosniff

<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType text/html "access plus 1 month"
</IfModule>



