/* Start Main Rulez */

body {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	font-size: 16px;
	min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

h1 {
	font-size: 42px;
	margin: 30px 0;
	font-weight: 700;
	color: #2c3e50;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.title {
	color: #2c3e50;
	font-weight: 600;
	transition: color 0.3s ease;
}

.title:hover {
	color: #3498db;
	text-decoration: none;
}

.well, aside {
	margin-top: 1.5rem;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 15px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.well:hover {
	transform: translateY(-5px);
	box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.btn-download{
	margin: 40px;
    padding: 15px 30px;
    font-size: 24px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 50px;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    border: none;
    border-radius: 25px;
    padding: 12px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #2980b9, #3498db);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.5);
}

.input-container {
	position: relative;
}

.asterisk {
    font-size: 20px;
    position: absolute;
    right: 10px;
    top: 7px;
    color: #D20707;
}

.main-form .asterisk {
    font-size: 30px;
    position: absolute;
    right: 30px;
    top: 8px;
    color: #D20707;
}

.nice-message {
	padding: 10px;
	background-color: #FFF;
	margin: 10px 0;
	border-left: 5px solid #080;
}

/* End Main Rulez */

/* Start Bootstrap Edits */

.navbar {
	border-radius: 0;
	margin-bottom: 0;
	background: rgba(44, 62, 80, 0.95) !important;
	backdrop-filter: blur(10px);
	box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
	border: none;
}

.nav > li > a,
.navbar-brand {
	padding: 15px 20px;
	transition: all 0.3s ease;
}

.navbar-brand {
	font-size: 2.2em;
    color: #ecf0f1 !important;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.navbar-brand:hover {
    color: #3498db !important;
    transform: scale(1.05);
}

.nav-link {
    color: #ecf0f1 !important;
    font-weight: 500;
    position: relative;
}

.nav-link:hover {
    color: #3498db !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: #3498db;
    transition: all 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
    left: 0;
}

.navbar-inverse .navbar-nav > .open>a,
.navbar-inverse .navbar-nav > .open>a:focus,
.navbar-inverse .navbar-nav > .open>a:hover,
.dropdown-menu {
    background-color: #3498db;
}

.dropdown-menu {
	min-width: 180px;
	padding: 0;
	font-size: 1em;
	border: none;
	border-radius: 0;
}

.dropdown-menu > li > a {
	color: #FFF;
	padding: 10px 15px;
}

.dropdown-menu > li > a:focus,
.dropdown-menu > li > a:hover {
    color: #FFF;
    background-color: #8e44ad;
}

.form-control {
	position: relative;
}

/* End Bootstrap Edits */

/* Start Header */

.upper-bar {
	padding: 10px;
	background-color: #FFF
}

.image {
	width: 300px;
	height: 300px;
	border-radius: 15px;
	object-fit: cover;
	transition: transform 0.3s ease;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.image:hover {
	transform: scale(1.05);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* End Header */

/* Additional Modern Styles */
.container {
    padding: 20px;
}

.item-box {
    border: none !important;
    padding: 25px;
    margin-bottom: 25px;
}

.price-tag {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.3);
}

.search-field {
    border-radius: 25px;
    border: 2px solid #3498db;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.search-field:focus {
    outline: none;
    border-color: #2980b9;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
}

.widget {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.widget-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 15px;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

/* Start Login Page */

.login-page form,
.the-errors {
	max-width: 380px;
	margin: auto;
}

.login-page form input {
	margin-bottom: 10px;
}

.login-page [data-class="login"].selected {
	color: #337AB7;
}

.login-page [data-class="signup"].selected {
	color: #5cb85c;
}

.login-page h1 {
	color: #C0C0C0;
}

.login-page h1 span {
	cursor: pointer;
}

.login-page .signup {
	display: none;
}

.the-errors .msg {
    padding: 10px;
    text-align: left;
    background-color: #fff;
    margin-bottom: 8px;
    border-right: 1px solid #e0e0e0;
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    color: #919191;
}

.the-errors .error {
    border-left: 5px solid #cd6858
}

/* End Login Page */

/* Start Categories Page */

.item-box {
	position: relative;
	background-color: #FFF;
}

.item-box .price-tag {
    background-color: #4B4B4B;
    padding: 2px 10px;
    position: absolute;
    right: 0;
    top: 10px;
    font-weight: bold;
    color: #FFF;
}

.item-box .approve-status {
    position: absolute;
    top: 40px;
    left: 0;
    background-color: #b85a5a;
    color: #FFF;
    padding: 3px 5px;
}

.item-box .caption p {
	height: 44px;
	max-height: 44px;
}

/* End Categories Page */

/* Start Show Item Page */

.item-info h2 {
	padding: 10px;
	margin: 0;
}

.item-info p {
	padding: 10px;
}

.item-info ul li { 
	padding: 10px;
}

.item-info ul li:nth-child(odd) {
	background-color: #e8e8e8;
}

.item-info ul li span {
	display: inline-block;
	width: 120px;
}

.tags-items a {
    display: inline-block;
    background-color: #e2e2e2;
    padding: 2px 10px;
    border-radius: 5px;
    color: #666;
    margin-right: 5px;
}


.search{
    padding: 4px 4px;
    margin-top: -5px;
}

.widget-title {
  margin-bottom: 10px;
  font-size: 20px;
  line-height: 1.5;
  text-transform: none;
}

.widget ul li {
  list-style-type: none;
  position: inherit;
  margin-bottom: .5em;
}
/* End Show Item Page */

/* Start Our Custom */

.custom-hr {
	border-top: 1px solid #c9c9c9;
}

/* End Our Custom */
a{
    text-decoration: aquamarine;
    color: #252525
}

.color-aside{
	background-color: #f8f8f8;
}

.set_mergin{
    margin-left: 30px;
}