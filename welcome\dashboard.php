<?php

	ob_start(); // Output Buffering Start

	if (isset($_COOKIE['User_Name'])) {

		$pageTitle = 'Dashboard';

		include 'init.php';

		/* Start Dashboard Page */

		$numItems = 6; // Number Of Latest Users

		$latestItems = getLatest("*", "programs", "Pro_ID", $numItems); // Latest Users Array

		$numCats = 6; // Number Of Latest Items

		$latestCats = getLatest("*", 'categories', 'Cat_ID', $numCats); // Latest Items Array
        
        $numreqs = 6; // Number Of Latest Items

		$latestReqs = getLatest("*", 'request', 'Req_ID', $numreqs); // Latest Items Array

		?>
<div class="row">
		<div class="home-stats">
            
			<div class=" container text-center">
				<h1>Dashboard</h1>	
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stat st-items">
                                <i class="fa fa-upload"></i>
                                <div class="info">
                                    Total Programs
                                    <span>
                                        <a href="items.php"><?php echo countItems('Pro_ID', 'programs') ?></a>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat st-comments">
                                <i class="fa fa-navicon"></i>
                                <div class="info">
                                    Total Categories
                                    <span>
                                        <a href="categories.php"><?php echo countItems('Cat_ID', 'categories') ?></a>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat st-request">
                                <i class="glyphicon glyphicon-pushpin"></i>
                                <div class="info">
                                    Total Requests
                                    <span>
                                        <a href="request.php"><?php echo countItems('Req_ID', 'request') ?></a>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
		  </div>

		<div class="latest">
			<div class="container">
				<div class="row">
					<div class="col-sm-4">
						<div class="panel panel-default">
							<div class="panel-heading">
								<i class="fa fa-upload"></i> 
								Latest <?php echo $numItems ?> Programs
								<span class="toggle-info pull-right">
									<i class="fa fa-plus fa-lg"></i>
								</span>
							</div>
							<div class="panel-body">
								<ul class="list-unstyled latest-users">
								<?php
									if (! empty($latestItems)) {
										foreach ($latestItems as $item) {
											echo '<li>';
												echo $item['Name'];
												echo '<a href="items.php?do=Edit&itemid=' . $item['Pro_ID'] . '">';
													echo '<span class="btn btn-success pull-right">';
														echo '<i class="fa fa-edit"></i> Edit';
													echo '</span>';
												echo '</a>';
											echo '</li>';
										}
									} else {
										echo 'There\'s No Members To Show';
									}
								?>
								</ul>
							</div>
						</div>
					</div>
					<div class="col-sm-4">
						<div class="panel panel-default">
							<div class="panel-heading">
								<i class="fa fa-navicon"></i> Latest <?php echo $numCats ?> Categories 
								<span class="toggle-info pull-right">
									<i class="fa fa-plus fa-lg"></i>
								</span>
							</div>
							<div class="panel-body">
								<ul class="list-unstyled latest-users">
									<?php
										if (! empty($latestCats)) {
											foreach ($latestCats as $cat) {
												echo '<li>';
													echo $cat['Cat_Name'];
													echo '<a href="categories.php?do=Edit&catid=' . $item['Cat_ID'] . '">';
														echo '<span class="btn btn-success pull-right">';
															echo '<i class="fa fa-edit"></i> Edit';
														echo '</span>';
													echo '</a>';
												echo '</li>';
											}
										} else {
											echo 'There\'s No Items To Show';
										}
									?>
								</ul>
							</div>
						</div>
					</div>
                    <div class="col-sm-4">
						<div class="panel panel-default">
							<div class="panel-heading">
								<i class="fa fa-navicon"></i> Latest <?php echo $numCats ?> Requests
								<span class="toggle-info pull-right">
									<i class="fa fa-plus fa-lg"></i>
								</span>
							</div>
							<div class="panel-body">
								<ul class="list-unstyled latest-users">
									<?php
                                        echo '<table class="main-table text-center table table-bordered">';
                                                echo '<tr>';
                                                    echo '<td>';
                                                        echo 'Real_IP';
                                                    echo '</td>';
                                                    echo '<td>';
                                                        echo 'Country';
                                                    echo '</td>';
                                                echo '</tr>';
										if (! empty($latestReqs)) {
											foreach ($latestReqs as $req) {
                                                echo '<tr>';
                                                    echo '<td>';
                                                        echo $req['Req_ip'];
                                                     echo '</td>';
                                                    echo '<td>';
                                                        echo $req['Country'];	
                                                    echo '</td>';
                                            echo '</tr>';
											}
                                            echo '</table>';
										} else {
											echo 'There\'s No Items To Show';
										}
									?>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<?php

		/* End Dashboard Page */

		include $tpl . 'footer.php';

	} else {

		header('Location: index.php');

		exit();
	}

	ob_end_flush(); // Release The Output

?>