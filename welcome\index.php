<?php
	$noNavbar = '';
	$pageTitle = 'Login';

	if (isset($_COOKIE['User_Name'])) {
		header('Location: dashboard.php'); // Redirect To Dashboard Page
        exit();
	}

	include 'init.php';

	// Check If User Coming From HTTP Post Request

	if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        /*try{
               // Store the IP address
                $req_ip = getVisIPAddr();
                $country = getViscountry($req_ip);
                }catch(Exception $e){
                
                $req_ip = "0.0.0.0";
                $country = "can not get country";
                echo 'Caught exception: ',  $e->getMessage(), "\n";
            }
            $stmt = $con->prepare("SELECT * FROM req_admin WHERE Req_ip = ?");

			// Execute Query

			$stmt->execute(array($req_ip));

			// Fetch The Data

			$req = $stmt->fetch();

			// The Row Count

			$check = $stmt->rowCount();

            if ($check > 0) {   
                $id = $req['Req_ID'];
                $ip = $req['Req_ip'];
                $count = $req['count'];
                
                if($count >= 5){
                     if(strpos($req_ip, $ip) === 0){ 
                            echo 'block';
                            die();
                        }  
                    }else{
                    
                        $count++;
                        $stmt = $con->prepare("UPDATE 
                                                    req_admin 
                                                SET 
                                                    count = ?
                                                WHERE 
                                                    Req_ID = ?");

                        $stmt->execute(array($count, $id));
                    }
                
            } else{
                // Insert Userinfo In Database
                $stmt = $con->prepare("INSERT INTO 

                    req_admin(Req_ip, Country, Date)

                    VALUES(:zip, :zcountry, now())");

                $stmt->execute(array(

                    'zip' 	=> $req_ip,
                    'zcountry' 	=> $country,


                ));
                
            }*/

		$username = $_POST['user'];
		$password = $_POST['pass'];
		$hashedPass = sha1($password);

		// Check If The User Exist In Database

		$stmt = $con->prepare("SELECT 
									User_ID, User_Name, Password 
								FROM 
									users 
								WHERE 
									User_Name = ? 
								AND 
									Password = ? 
								LIMIT 1");

		$stmt->execute(array($username, $hashedPass));
		$row = $stmt->fetch();
		$count = $stmt->rowCount();

		// If Count > 0 This Mean The Database Contain Record About This Username

		if ($count > 0) {
			
            setcookie("User_Name", $row['User_ID'], time()+36000, '/', null, null, true);
			header('Location: dashboard.php'); // Redirect To Dashboard Page
			exit();
		}else{
            $error = "Error";
             
        }

	}

    //count requests
    function getVisIpAddr() {
      
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        }
        else if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        }
        else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
    function getViscountry($ip) {
        $dataArray = json_decode(file_get_contents("http://www.geoplugin.net/json.gp?ip=".$ip));
        $country =$dataArray->geoplugin_countryName;
        if (!empty($country)) {
            return $country;
        }
        else {
            return "can not get country";
        }
    }
    
?>

	<form class="login" action="<?php echo htmlentities($_SERVER['PHP_SELF']); ?>" method="POST">
		<h4 class="text-center">Admin Page</h4>
		<input class="form-control" type="text" name="user" autocomplete="off" />
		<input class="form-control" type="password" name="pass" autocomplete="off"/>
		<input class="btn btn-primary btn-block" type="submit" value="login" />
	</form>
    <?php
    if (isset($error)) {
       echo '<div class="the-errors text-center">';
            echo '<div class="msg error">' . $error . '</div>';
        echo '</div>';
			}

include $tpl . 'footer.php'; ?>