<?php

	// Error Reporting

	ini_set('display_errors', 'on');
	error_reporting(E_ALL);

	include 'welcome/connect.php';

	// Routes

	$tpl 	= 'includes/templates/'; // Template Directory
	$func	= 'includes/functions/'; // Functions Directory
	$css 	= 'layout/css/'; // Css Directory
	$js 	= 'layout/js/'; // Js Directory

	// Include The Important Files

	include $func . 'functions.php';
	include $tpl . 'header.php';
    

    if(!isset($_COOKIE["freecrack"])){
    //count requests
    function getVisIpAddr() {
      
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        }
        else if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        }
        else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }/*
    function getViscountry($ip) {
        $dataArray = json_decode(file_get_contents("http://www.geoplugin.net/json.gp?ip=".$ip));
        $country =$dataArray->geoplugin_countryName;
        if (!empty($country)) {
            return $country;
        }
        else {
            return "can not get country";
        }
    }*/
    // Store the IP address
   $ip = getVisIPAddr();
   // $country = getViscountry($ip);

    // Insert Userinfo In Database
   $stmt = $con->prepare("INSERT INTO 

        request(Req_ip, Country, Date)

        VALUES(:zip, :zcountry, now())");

    $stmt->execute(array(

        'zip' 	=> $ip,
        'zcountry' 	=> "",
        

    ));
        //set cookies
        setcookie('freecrack', "request", time()+36000, '/');
    }